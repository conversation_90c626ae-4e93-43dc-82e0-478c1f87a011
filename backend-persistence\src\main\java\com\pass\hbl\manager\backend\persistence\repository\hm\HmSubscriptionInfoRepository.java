package com.pass.hbl.manager.backend.persistence.repository.hm;

import com.pass.hbl.manager.backend.persistence.entity.hm.HmSubscriptionInfo;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

public interface HmSubscriptionInfoRepository extends PagingAndSortingRepository<HmSubscriptionInfo, UUID> {

    Optional<HmSubscriptionInfo> findByUserIdAndSubscriptionId(UUID userId, String subscriptionId);

    @Modifying
    @Query("UPDATE HmSubscriptionInfo s SET s.lastPaymentAmount = :amount, s.lastPaymentDate = :date, " +
            "s.lastPaymentId = :paymentId, s.modifiedAt = CURRENT_TIMESTAMP WHERE s.id = :id")
    int updatePaymentInfo(@Param("id") UUID id, @Param("amount") BigDecimal amount,
                          @Param("date") LocalDateTime date, @Param("paymentId") String paymentId);

    @Modifying
    @Query("UPDATE HmSubscriptionInfo s SET s.endDate = :endDate, s.modifiedAt = CURRENT_TIMESTAMP " +
            "WHERE s.id = :id")
    int updateEndDate(@Param("id") UUID id, @Param("endDate") LocalDateTime endDate);
}