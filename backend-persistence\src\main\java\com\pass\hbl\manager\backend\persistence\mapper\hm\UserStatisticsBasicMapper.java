package com.pass.hbl.manager.backend.persistence.mapper.hm;

import com.pass.hbl.manager.backend.persistence.domain.hm.UserStatisticsBasicDO;
import com.pass.hbl.manager.backend.persistence.dto.hm.UserStatisticsDto;
import com.pass.hbl.manager.backend.persistence.service.hm.handlers.UserLoginHandler;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static java.util.Objects.isNull;

/**
 * Mapper for converting UserStatisticsBasicDO to UserStatisticsDto objects
 * This mapper handles the basic user profile data mapping
 * Additional data like leagues and statistics need to be set separately
 */
@Component
public class UserStatisticsBasicMapper {

    private final UserLoginHandler userLoginHandler;

    public UserStatisticsBasicMapper(UserLoginHandler userLoginHandler) {
        this.userLoginHandler = userLoginHandler;
    }

    /**
     * Map a UserStatisticsBasicDO to a UserStatisticsDto
     *
     * @param userBasicData The basic user data
     * @return A UserStatisticsDto with the basic user data
     */
    public UserStatisticsDto mapToDto(UserStatisticsBasicDO userBasicData) {
        if (userBasicData == null) {
            return null;
        }

        UserStatisticsDto userDataDto = new UserStatisticsDto();

        userDataDto.setSso_id(userBasicData.getSsoId());
        userDataDto.setLanguage(userBasicData.getAppLanguage());
        userDataDto.setManagerLevel(userBasicData.getLevel());
        userDataDto.setAppVersion(userBasicData.getAppVersion());
        userDataDto.setUsername(userBasicData.getUsername());
        userDataDto.setRegisteredSince(userBasicData.getCreatedAt());

        // Get last login from user_login table, fall back to fixed date (2000-01-01) if not found
        LocalDateTime lastLoginDate = !isNull(userLoginHandler.getLastLogin(userBasicData.getId())) ? userLoginHandler.getLastLogin(userBasicData.getId()).atStartOfDay() : null ;
        userDataDto.setLastLogin(lastLoginDate);

        userDataDto.setPremium(userBasicData.getPremium());
        userDataDto.setPremiumExpiration(userBasicData.getPremiumExpiration());
        userDataDto.setAccountDeleted(userBasicData.getDeleted());
        userDataDto.setChangedAfter(null); // Default empty value

        return userDataDto;
    }
}
