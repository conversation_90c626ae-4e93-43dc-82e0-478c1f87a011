package com.pass.hbl.manager.backend.persistence.entity.hm;

import com.pass.hbl.manager.backend.persistence.entity.AbstractEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.UUID;

@Table(name = "league_invitation", schema = "hm", catalog = "handball_manager")
@NoArgsConstructor
@Getter
@Setter
@ToString
@Entity
@SQLDelete(sql = "UPDATE hm.league_invitation SET deleted = true, deleted_at = now() WHERE id=?")
@Where(clause = "deleted=false")
public class HmLeagueInvitation extends AbstractEntity {

    @Id
    @GeneratedValue(generator = "uuid2")
    @Column(name = "id")
    private UUID id;

    @NotNull
    @Column(name = "valid_until")
    private LocalDateTime validUntil;

    @NotNull
    @ToString.Exclude
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "league_id", referencedColumnName = "id", updatable = false)
    private HmLeague league;

    public HmLeagueInvitation(LocalDateTime validUntil, HmLeague league) {
        this.validUntil = validUntil;
        this.league = league;
    }
}
